namespace PBC.AggregatorService.DTOs
{
    #region ::: SelectDealerName :::
    public class SelectDealerNameList
    {
        public string value { get; set; } = string.Empty;
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; } = string.Empty;
    }
    #endregion

    public class getProductWarrantyList
    {
        public int Company_ID { get; set; }
        public int ModelID { get; set; }
        public int Branch { get; set; }
        public string SerialNumber { get; set; }

    }
    public class SelectSRDetailsRequest
    {
        public GNM_User User { get; set; } = new GNM_User();
        public int ServiceRequestID { get; set; }
        public int ChildTicketSequenceID { get; set; }
        public int BranchID { get; set; }
        public string? ConnectionString { get; set; }
        public bool IsReopen { get; set; }
        public bool IsOemDashboard { get; set; }
        public int? OemCompanyId { get; set; }
        public string UserCulture { get; set; } = string.Empty;
        public string GeneralLanguageCode { get; set; } = string.Empty;
        public string UserLanguageCode { get; set; } = string.Empty;
        public int MenuID { get; set; }
        public DateTime LoggedInDateTime { get; set; }
    }

    public class GetActionsList
    {
        public int WFCurrentStepID { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int Branch { get; set; }
    }
    public class GetRolesForActionsList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int WFCurrentStepID { get; set; }
        public int ActionID { get; set; }
        public int TransactionID { get; set; }
    }
    public class GetProductDetailsFList
    {
        public int Company_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int PartyID { get; set; }
        public string User_EmailID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
    }
    public class GetPartyDetailsbyIDEList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int PartyID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
        public string UserCulture { get; set; }
    }
    public class getProductUniqueNumberFList
    {
        public int Company_ID { get; set; }
        public int ModelID { get; set; }
        public string SerialNumber { get; set; }

    }
    public class getBrandProductTypecList
    {
        public int UserLanguageID { get; set; }
        public int ModelID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
    }

    public class loadIssueSubAreaList
    {
        public int IssueAreaID { get; set; }
        public int EnquiryType_ID { get; set; }
        public int Language_ID { get; set; }
        public int Company_ID { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string UserLanguageCode { get; set; }
    }
    public class GetScheduledDropinsList
    {
        public int Company_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
    }
    public partial class GNM_User
    {
        public string User_Name { get; set; }
        public string LandingPage { get; set; }
        public int User_ID { get; set; }
        public string User_LoginID { get; set; }
        public string User_Password { get; set; }
        public string User_IPAddress { get; set; }
        public bool ReleaseVersionPopup { get; set; }
        public int Company_ID { get; set; }
        public int Language_ID { get; set; }
    }
    public class GetContactPersonDetailsList
    {
        public int ContactPersonID { get; set; }
    }
    public class HD_LoadContractorContactList
    {
        public int CustomerID { get; set; }
        public int Company_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public string SerialNum { get; set; }
    }
    public class getSerialNumberForModelFList
    {
        public int Company_ID { get; set; }
        public int Party_ID { get; set; }
        public int ModelID { get; set; }
    }
    public class InitialModeRequest
    {
        public string UserEmployeeId { get; set; } = string.Empty;
        public int CompanyId { get; set; }
        public string UserCulture { get; set; } = string.Empty;
        public string? UnRegServiceRequestId { get; set; }
        public string? PartyId { get; set; }
        public string? ModelId { get; set; }
        public string? SerialNumber { get; set; }
        public string? RequestDesc { get; set; }
        public string? ModelName { get; set; }
        public string? Unique { get; set; }
        public string? ServiceRequestId { get; set; }
        public string? Reopen { get; set; }
        public int? Mode { get; set; }
        public int? StatusId { get; set; }
        public int? CompanyIdAlt { get; set; }
    }

    #region ::: InitialSetup :::
    public class InitialSetupList
    {
        public int ObjectId { get; set; }
        public int User_ID { get; set; }
        public string HelpDesk { get; set; } = string.Empty;
        public int Company_ID { get; set; }
        public bool NeedToChangepassword { get; set; }
    }
    #endregion

    public class UnLockRecordList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int ServiceRequestID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; }
    }

    public class BufferRequest
    {
        public string Name { get; set; }
        public string WFName { get; set; }
        public string HelpDesk { get; set; }
        public int CompanyId { get; set; }
        public int UserId { get; set; }
        public string connectionString { get; set; }
    }

    public class SelectAllDropdownDataList
    {
        public int Company_ID { get; set; }
        public int IssueAreaId { get; set; }
        public int User_ID { get; set; }
        public int User_Employee_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
    }

    #region ::: CheckAddPermissions :::
    public class CheckAddPermissionsRequest
    {
        public string Name { get; set; } = string.Empty;
        public string WFName { get; set; } = string.Empty;
        public string HelpDesk { get; set; } = string.Empty;
        public int CompanyId { get; set; }
        public int UserId { get; set; }
    }
    #endregion

    #region ::: ValidateCalldateAndPCD :::
    public class validateCalldateandPCDList
    {
        public DateTime PCD { get; set; }
        public DateTime Calldate { get; set; }
        public string User_EmailID { get; set; }
    }
    #endregion

    #region ::: CheckBayWorkshopAvailability :::
    public class CheckBayWorkshopAvailabilityList
    {
        public DateTime ExpectedArrivalDate { get; set; }
        public DateTime ExpectedDepartureDate { get; set; }
        public bool IsFromQuote { get; set; }
        public int Quotation_ID { get; set; }
        public int Branch { get; set; }
        public int VIN { get; set; }
        public int ServiceRequest_ID { get; set; }
    }
    #endregion

    #region ::: CheckForWorkshopBlockOverlap :::
    public class CheckForWorkshopBlockOverlapList
    {
        public DateTime ExpectedArrivalDate { get; set; }
        public DateTime ExpectedDepartureDate { get; set; }
        public bool IsFromQuote { get; set; }
        public int Quotation_ID { get; set; }
        public int Branch { get; set; }
        public int VIN { get; set; }
        public int ServiceRequest_ID { get; set; }
    }
    #endregion

    #region ::: SelectFieldSearchParty :::
    public class SelectFieldSearchParty2List
    {
        public string starts_with { get; set; } = string.Empty;
        public int type { get; set; }
        public int LangID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
    }

    public class SelectFieldSearchPartyRequest
    {
        public SelectFieldSearchParty2List Obj { get; set; } = new();
        public string Sidx { get; set; } = string.Empty;
        public string Sord { get; set; } = string.Empty;
        public int Page { get; set; }
        public int Rows { get; set; }
        public bool Search { get; set; }
        public string Filters { get; set; } = string.Empty;
        public string User_EmailID { get; set; }

    }
    #endregion

    #region ::: GetCustomerData :::
    public class GetDataUserLindingList
    {
        public string starts_with { get; set; } = string.Empty;
        public int type { get; set; }
        public int LangID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
    }

    public class GetCustomerDataRequest
    {
        public GetDataUserLindingList Obj { get; set; } = new();
        public string User_EmailID { get; set; }
    }
    #endregion

    #region ::: GetDealerData :::
    public class GetDealerDataList
    {
        public string starts_with { get; set; } = string.Empty;
        public int type { get; set; }
        public int LangID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
    }

    public class GetDealerDataRequest
    {
        public GetDealerDataList Obj { get; set; } = new();
        public string User_EmailID { get; set; }
    }
    #endregion

    #region ::: GetProductDetails :::
    public class GetProductDetailsUserLandingList
    {
        public int ProductID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }
    public class GetOpenCampaignDetailsReq
    {
        public GetOpenCampaignDetailsLst Obj { get; set; }
        public string Sidx { get; set; }
        public int Rows { get; set; }
        public int Page { get; set; }
        public string Sord { get; set; }
        public bool Search { get; set; }
        public long Nd { get; set; }
        public string Filters { get; set; }
        public bool Advnce { get; set; }
        public string Query { get; set; }
    }
    public class GetOpenCampaignDetailsLst
    {
        public int Product_ID { get; set; }
        public int Company_ID { get; set; }
    }
    public class Attachements
    {
        public int ATTACHMENTDETAIL_ID { get; set; }
        public string AttachmentIDS { get; set; } = string.Empty;
        public int TransactionID { get; set; }
        public string FILE_NAME { get; set; } = string.Empty;
        public string FILEDESCRIPTION { get; set; } = string.Empty;
        public string UPLOADBY { get; set; } = string.Empty;
        public DateTime UPLOADDATE { get; set; }
        public string UPLOADDATESORT { get; set; } = string.Empty;
        public string delete { get; set; } = string.Empty;
        public int Upload { get; set; }
        public string view { get; set; } = string.Empty;
        public int OBJECTID { get; set; }
        public string DocumentType { get; set; } = string.Empty;
        public int DocumentType_ID { get; set; }
        public int? DetailID { get; set; }
        public int ID { get; set; }
        public string edit { get; set; } = string.Empty;
        public string Tablename { get; set; } = string.Empty;
        public int PartID { get; set; }
        public string TransactionType { get; set; } = string.Empty;
        public string TransactionNumber { get; set; } = string.Empty;
        public string Date { get; set; } = string.Empty;
        public string Amount { get; set; } = string.Empty;
        public int ModelID { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Remarks { get; set; } = string.Empty;
    }
    public class EditServiceRequest
    {
        public string Data { get; set; }
        public int BranchID { get; set; } 
        public string UserLanguageCode { get; set; }
        public string HDAttachmentData { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public string HelpDesk { get; set; }
        public string HelpLineNumber { get; set; }
        public int Language_ID { get; set; }
        public int Employee_ID { get; set; }
        public int MenuID { get; set; }
        public string HolidayDetails { get; set; }

        public string NotesData { get; set; }
        public string PartsDetailData { get; set; }
        public string TMLPartsDetailData { get; set; }
        public string FollowUpsDetailData { get; set; }
        public string ProductDetailsData { get; set; }
        public string ContactPersonObj { get; set; }
        public string UserCulture { get; set; }
        public string ReLoginView { get; set; }
        public string AttachmentDelete { get; set; }
        public string ObjectID { get; set; }
        public string LoggedINDateTime { get; set; }
        public string ProductCustomerID { get; set; }
        public string QuestionData { get; set; }

        public string GeneralLanguageCode { get; set; }
        public string Reopen { get; set; }
    }
    public class ServiceRequest_SelectList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int Language_ID { get; set; }
        public int Employee_ID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string NotesData { get; set; }
        public string PartsDetailData { get; set; }
        public string TMLPartsDetailData { get; set; }
        public string FollowUpsDetailData { get; set; }
        public string ProductDetailsData { get; set; }
        public string ContactPersonObj { get; set; }
        public string AttachmentsData { get; set; }
        public string UserCulture { get; set; }
        public string ReLoginView { get; set; }
        public string AttachmentDelete { get; set; }
        public string ObjectID { get; set; }
        public string LoggedINDateTime { get; set; }
        public string ProductCustomerID { get; set; }
        public string MenuID { get; set; }
        public string Data { get; set; }
        public string Reopen { get; set; }
        public string QuestionData { get; set; }
        public string HelpDesk { get; set; }
        public string HelpLineNumber { get; set; }
        public string HolidayDetails { get; set; }
    }
    public class InsertSRRequest
    {
        public string Reopen { get; set; } = string.Empty;
        public string Data { get; set; } = string.Empty;
        public int BranchID { get; set; }
        public string UserLanguageCode { get; set; } = string.Empty;
        public List<string> RequestParams { get; set; } = new List<string>();
        public List<Attachements> HDAttachmentData { get; set; } = new List<Attachements>();
        public string Path { get; set; } = string.Empty;
        public int LogException { get; set; } = 1;
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public string HelpDesk { get; set; } = string.Empty;
        public string HelpLineNumber { get; set; } = string.Empty;
        public int Language_ID { get; set; }
        public int Employee_ID { get; set; }
        public int MenuID { get; set; }
        public string HolidayDetails { get; set; } = string.Empty;
        public bool IsFromWebAPI { get; set; } = false;
    }
    public class ValidateReadingList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int ModelID { get; set; }
        public int Reading { get; set; }
        public string SerialNumber { get; set; }
    }

    public class GetProductDetailsRequest
    {
        public GetProductDetailsUserLandingList Obj { get; set; } = new();
        public string User_EmailID { get; set; }
    }
    #endregion

    #region ::: CheckDuplicateContactPerson :::
    public class checkDuplicateContactPersonList
    {
        public string CPName { get; set; } = string.Empty;
        public int Party_ID { get; set; }
    }

    public class CheckDuplicateContactPersonRequest
    {
        public checkDuplicateContactPersonList Obj { get; set; } = new();
    }
    #endregion

    #region ::: GetOpenCampaignDetails :::
    public class GetOpenCampaignDetailsList
    {
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
        public string CompanyIDs { get; set; } = string.Empty;
        public string BranchIDs { get; set; } = string.Empty;
        public int Branch { get; set; }
        public int Language_ID { get; set; }
        public string GeneralLanguageCode { get; set; } = string.Empty;
        public string UserLanguageCode { get; set; } = string.Empty;
        public string UserCulture { get; set; } = string.Empty;
        public int mode { get; set; }
        public int FromManager { get; set; }
        public int StatusID { get; set; }
        public string Legendfilter { get; set; } = "All";
    }

    public class GetOpenCampaignDetailsRequest
    {
        public GetOpenCampaignDetailsList Obj { get; set; } = new();
        public string Sidx { get; set; } = string.Empty;
        public int Rows { get; set; }
        public int Page { get; set; }
        public string Sord { get; set; } = string.Empty;
        public bool Search { get; set; }
        public long Nd { get; set; }
        public string Filters { get; set; } = string.Empty;
        public bool Advnce { get; set; }
        public string Query { get; set; } = string.Empty;
    }
    #endregion

    #region ::: GetInitialData :::
    public class GetInitialDataList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
        public string UserCulture { get; set; } = string.Empty;
        public string User_EmailID { get; set; }
    }
    #endregion

    #region ::: SelectServiceRequest :::
    public class SelectServiceRequestList
    {
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
        public string CompanyIDs { get; set; } = string.Empty;
        public string BranchIDs { get; set; } = string.Empty;
        public int Branch { get; set; }
        public int Language_ID { get; set; }
        public string GeneralLanguageCode { get; set; } = string.Empty;
        public string UserLanguageCode { get; set; } = string.Empty;
        public string UserCulture { get; set; } = string.Empty;
        public int mode { get; set; }
        public int FromManager { get; set; }
        public int StatusID { get; set; }
        public string Legendfilter { get; set; } = "All";
    }

    public class SelectServiceRequestRequest
    {
        public SelectServiceRequestList Obj { get; set; } = new();
        public string Sidx { get; set; } = string.Empty;
        public int Rows { get; set; }
        public int Page { get; set; }
        public string Sord { get; set; } = string.Empty;
        public bool Search { get; set; }
        public long Nd { get; set; }
        public string Filters { get; set; } = string.Empty;
        public bool Advnce { get; set; }
        public string Query { get; set; } = string.Empty;
        public string User_EmailID { get; set; }
        public string mode { get; set; }
        public string StatusID { get; set; }
        public string Legendfilter { get; set; }
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
        public string CompanyIDs { get; set; } = string.Empty;
        public string BranchIDs { get; set; } = string.Empty;

    }
    #endregion

    #region ::: GetWorkFlowSummary :::
    public class GetWorkFlowSummaryList
    {
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int User_ID { get; set; }
        public string User_EmailID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; } = string.Empty;
    }
    #endregion

    #region ::: SaveCustomer :::
    public class SaveCustomerList
    {
        public int Branch { get; set; }
        public string key { get; set; } = string.Empty;
        public string PartyAddress { get; set; } = string.Empty;
        public string PartyTaxDetails { get; set; } = string.Empty;
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
    }
    #endregion

    #region ::: TabPosition :::
    public class TabPosition
    {
        public int ID { get; set; }
        public bool Visibility { get; set; }
        public int Position { get; set; }
    }
    #endregion

    #region ::: UpdateIsEditTicket :::
    public class UpdateIsEditTicketList
    {
        public int ServiceRequest_ID { get; set; }
        public int Type { get; set; } // 1 - editing, 2 - saved
        public int User_ID { get; set; }
        public string User_EmailID { get; set; }
    }
    #endregion
}
