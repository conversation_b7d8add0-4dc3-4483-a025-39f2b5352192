namespace PBC.AggregatorService.DTOs
{
    /// <summary>
    /// DTO for Get SR Role Access request
    /// </summary>
    public class GetSRRoleAccessRequest
    {
        public string Name { get; set; }
        public int UserID { get; set; }
    }

    /// <summary>
    /// DTO for Select All Attachment request
    /// </summary>
    public class SelAllAttachmentRequest
    {
        public SelAllAttachmentList SelAllAttachmentObj { get; set; }

        public string ConnString { get; set; }

        public int LogException { get; set; }

        public string Sidx { get; set; }

        public string Sord { get; set; }

        public int Page { get; set; }

        public int Rows { get; set; }

        public bool _search { get; set; }

        public string Filters { get; set; }
    }
    public class SelAllAttachmentList
    {
        public int TransactionID { get; set; }
        public int DetailID { get; set; }
        public int LangID { get; set; }
        public string ObjectNames { get; set; }
        public string Tablename { get; set; }
        public string UserCulture { get; set; }
        public string AttachmentData { get; set; }
        public string AppPathString { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }

    public class DocExportRequest
    {
        public int Company_ID { get; set; }
        public int? Branch { get; set; }
        public System.Data.DataTable? dt { get; set; }
        public System.Data.DataTable? dt1 { get; set; }
        public string? FileName { get; set; }
        public string? Header { get; set; }
        public int exprtType { get; set; }
        public string? UserCulture { get; set; }
        public System.Data.DataTable? DtCriteria { get; set; }
    }

    public class FileExportResult
    {
        public byte[] FileContents { get; set; }
        public string ContentType { get; set; }
        public string FileName { get; set; }
    }
}
