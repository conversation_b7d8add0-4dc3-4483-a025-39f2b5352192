﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.Linq;
using PBC.UtilityService.Utilities.DTOs;
using LS = PBC.UtilityService.Utilities;
using System.Net;
using ClosedXML.Excel;

namespace PBC.UtilityService.Utilities
{

    public static class ReturnIPAddress
    {
        //public static string ReturnIP()
        //{
        //    string VisitorsIPAddr = string.Empty;
        //    // First Method
        //    if (HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
        //    {
        //        VisitorsIPAddr = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"].ToString();
        //    }
        //    else if (HttpContext.Current.Request.UserHostAddress.Length != 0)
        //    {
        //        VisitorsIPAddr = HttpContext.Current.Request.UserHostAddress;
        //    }

        //    return VisitorsIPAddr;
        //}
        // public static string ReturnIP1()
        //{
        //    string VisitorsIPAddr = string.Empty;
        //    // First Method
        //    if (HttpContext.Current.Request.ServerVariables["HTTP_CLIENT_IP"] != null)
        //    {
        //        VisitorsIPAddr = HttpContext.Current.Request.ServerVariables["HTTP_CLIENT_IP"].ToString();

        //        if (!string.IsNullOrEmpty(VisitorsIPAddr))
        //         {
        //             string[] ipRange = VisitorsIPAddr.Split(',');
        //             VisitorsIPAddr = ipRange[0].Trim();
        //         }
        //    }
        //    return VisitorsIPAddr;
        //}
        //public static string ReturnIP()
        //{
        //    string VisitorsIPAddr = string.Empty;
        //    if (HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"] != null)
        //    {
        //        VisitorsIPAddr = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"].ToString();
        //        if (!string.IsNullOrEmpty(VisitorsIPAddr))
        //        {
        //            string[] ipRange = VisitorsIPAddr.Split(',');
        //            VisitorsIPAddr = ipRange[0].Trim();
        //        }
        //    }
        //    return VisitorsIPAddr;
        //}

    }

    public class DocumentExport
    {

        #region ::: DocumentExport Service Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// Generates a file as a byte array and returns it along with metadata.
        /// This service method is designed to be called by a controller.
        /// </summary>
        /// <returns>A FileExportResult object containing the file bytes, content type, and name, or null if an error occurs.</returns>
        public static async Task<FileExportResult> Export(ExportList ExportObj, string connstring, int LogException)
        {
            string companyName = "", BranchName = "", BranchAddress = "";
            int colCount = ExportObj.dt.Columns.Count;

            try
            {
                using (var conn = new SqlConnection(connstring))
                {
                    using (var command = new SqlCommand("UP_Export_AM_ERP_Export_Common", conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", ExportObj.Company_ID);
                        command.Parameters.AddWithValue("@BranchID", Convert.ToInt32(ExportObj.Branch?.ToString() ?? "0"));

                        await conn.OpenAsync();
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                companyName = reader["Company_Name"] as string;
                                BranchName = reader["Branch_Name"] as string;
                                BranchAddress = reader["Branch_Address"] as string;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return null; // Indicate failure to the controller
            }

            try
            {
                if (ExportObj.exprtType == 1)
                {
                    using (var workbook = new XLWorkbook())
                    {
                        var worksheet = workbook.Worksheets.Add("Report");
                        int currentRow = 1;

                        // --- Main Report Title ---
                        var titleCell = worksheet.Cell(currentRow, 1);
                        titleCell.Value = ExportObj.Header;
                        worksheet.Range(currentRow, 1, currentRow, colCount).Merge().Style
                            .Font.SetBold(true)
                            .Font.SetFontSize(16)
                            .Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        currentRow += 2;

                        // --- Company & Branch Details ---
                        worksheet.Cell(currentRow, 1).Value = CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "CompanyName") + ":";
                        worksheet.Cell(currentRow, 2).Value = companyName;
                        worksheet.Cell(currentRow, 1).Style.Font.SetBold(true).Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);
                        currentRow++;

                        worksheet.Cell(currentRow, 1).Value = CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "BranchName") + ":";
                        worksheet.Cell(currentRow, 2).Value = BranchName;
                        worksheet.Cell(currentRow, 1).Style.Font.SetBold(true).Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);
                        currentRow++;

                        worksheet.Cell(currentRow, 1).Value = CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "BranchAddress") + ":";
                        worksheet.Cell(currentRow, 2).Value = BranchAddress;
                        worksheet.Cell(currentRow, 1).Style.Font.SetBold(true).Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);
                        currentRow += 2;

                        for (int i = 0; i < ExportObj.dt.Rows.Count; i++)
                        {
                            var dataRow = ExportObj.dt.Rows[i];
                            var excelRow = worksheet.Row(currentRow + i);

                            string firstColValue = dataRow[0]?.ToString() ?? "";
                            if (firstColValue == CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "GrandTotalSumofallDaysindate") || firstColValue.Contains("Sub-Total"))
                            {
                                excelRow.Style.Font.SetBold(true);
                            }

                            for (int k = 0; k < colCount; k++)
                            {
                                var cell = excelRow.Cell(k + 1);

                                string alignCode = ExportObj.dt1.Rows[0][k].ToString();
                                cell.Style.Alignment.Horizontal = alignCode switch
                                {
                                    "0" => XLAlignmentHorizontalValues.Left,
                                    "1" => XLAlignmentHorizontalValues.Center,
                                    _ => XLAlignmentHorizontalValues.Right
                                };

                                string colName = ExportObj.dt.Columns[k].ColumnName;
                                if (colName == CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "ClockInDateTime") ||
                                    colName == CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "CustomerAccountNumber"))
                                {
                                    cell.Style.NumberFormat.Format = "@";
                                }
                            }
                        }

                        worksheet.Columns().AdjustToContents();

                        using (var stream = new MemoryStream())
                        {
                            workbook.SaveAs(stream);

                            return new FileExportResult
                            {
                                FileContents = stream.ToArray(),
                                ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                FileName = ExportObj.FileName + ".xlsx"
                            };
                        }
                    }
                }
                else
                {
                    using (var ms = new MemoryStream())
                    {
                        Document document = new Document(PageSize.A4.Rotate());
                        PdfWriter.GetInstance(document, ms);
                        document.Open();

                        Paragraph title = new Paragraph(ExportObj.Header, FontFactory.GetFont("Arial", 16, Font.BOLD)) { Alignment = Element.ALIGN_CENTER };
                        document.Add(title);
                        document.Add(new Paragraph(" ")); // Spacing

                        document.Add(new Paragraph($"{CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "CompanyName")}: {companyName}"));
                        document.Add(new Paragraph($"{CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "BranchName")}: {BranchName}"));
                        document.Add(new Paragraph($"{CommonFunctionalities.GetResourceString(ExportObj.UserCulture, "BranchAddress")}: {BranchAddress}"));
                        document.Add(new Paragraph(" ")); // Spacing

                        PdfPTable dataTable = new PdfPTable(colCount) { WidthPercentage = 100 };
                        foreach (DataColumn column in ExportObj.dt.Columns)
                        {
                            dataTable.AddCell(new PdfPCell(new Phrase(column.ColumnName)) { BackgroundColor = BaseColor.LIGHT_GRAY, HorizontalAlignment = Element.ALIGN_CENTER });
                        }
                        foreach (DataRow row in ExportObj.dt.Rows)
                        {
                            foreach (var item in row.ItemArray)
                            {
                                dataTable.AddCell(new Phrase(item.ToString()));
                            }
                        }
                        document.Add(dataTable);

                        document.Close();

                        return new FileExportResult
                        {
                            FileContents = ms.ToArray(),
                            ContentType = "application/pdf",
                            FileName = ExportObj.FileName + ".pdf"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        #endregion

        public static class DashBoardExport
        {
            #region :::DashBoardExport   Uday Kumar J B 21-08-2024 :::
            /// <summary>
            /// To Get DashBoardExport 
            /// </summary>
            /// 
            public static async Task<JsonResult> Export(DashBoardExportList Obj, string connstring, int LogException)
            {
                string bid = Obj.Branch == null ? "0" : Obj.Branch.ToString();
                int GeneralLangauageID = Convert.ToInt32(Obj.GeneralLanguageID);
                int UserLanagauageID = Convert.ToInt32(Obj.UserLanguageID);
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                int BranchID = Convert.ToInt32(bid);

                string cName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CompanyName").ToString();
                string BName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "BranchName").ToString();
                string FD = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "fromdate").ToString();
                string TD = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ToDate").ToString();
                string companyName = "";
                string BranchName = "";
                SqlCommand command = null;
                using (SqlConnection conn = new SqlConnection(connstring))
                {
                    string query = "UP_Export_AM_ERP_Export_CommonReportExport";


                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                            command.Parameters.AddWithValue("@BranchID", BranchID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", GeneralLangauageID);
                            command.Parameters.AddWithValue("@UserLanguageID", UserLanagauageID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    companyName = reader["Company_Name"] as string;
                                    BranchName = reader["Branch_Name"] as string;
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                string ReportImagePath = "";
                if (System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath") != null)
                    ReportImagePath = System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath").ToString();
                try
                {
                    int colCount = Obj.dt.Columns.Count;
                    DateTime? FromDate = Obj.FromDate;
                    if (FromDate == null)
                    {
                        using (SqlConnection conn = new SqlConnection(connstring))
                        {
                            string query = @"SELECT CAST(Company_FinancialYear_FromDate AS DATE) 
                       FROM GNM_CompanyFinancialYear 
                       WHERE GETDATE() BETWEEN Company_FinancialYear_FromDate AND Company_FinancialYear_ToDate 
                       AND Company_ID = @Company_ID";


                            try
                            {
                                using (command = new SqlCommand(query, conn))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.Parameters.AddWithValue("@Company_ID", Company_ID);

                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    object result = command.ExecuteScalar();

                                    if (result != null && result != DBNull.Value)
                                    {
                                        FromDate = Convert.ToDateTime(result);
                                    }
                                    else
                                    {
                                        FromDate = null;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                command.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }
                    }
                    else
                    {
                        FromDate = Obj.FromDate;
                    }
                    DateTime? ToDate = (Obj.ToDate == null) ? DateTime.Now : Obj.ToDate;
                    string FromdateString = Convert.ToDateTime(FromDate).ToString("dd-MMM-yyyy");
                    string TodateString = Convert.ToDateTime(ToDate).ToString("dd-MMM-yyyy");
                    StringBuilder sb = new StringBuilder();
                    sb.Append("<table>");
                    // added by shivanand 14-Jun-2018 started
                    if (ReportImagePath != "")
                        sb.Append("</tr><td <img src='" + ReportImagePath + "'></img></td><tr></tr>");
                    sb.Append("<tr></tr>");
                    sb.Append("<tr><td colspan='" + colCount + "' style='text-align:center'>");
                    if (Obj.Header != null && Obj.Header != "")
                    {
                        sb.Append("<B>" + Obj.Header + "</B>");
                    }
                    sb.Append("</td></tr>");
                    sb.Append("<tr></tr>");
                    sb.Append("<tr><td style='text-align:right'><B>" + cName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + companyName + "</td></tr>");
                    sb.Append("<tr><td style='text-align:right'><B>" + BName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + BranchName + "</td></tr>");
                    sb.Append("<tr><td style='text-align:right'><B>" + FD + "</B> :</td><td style='text-align:left' >" + FromdateString + "</td></tr>");
                    sb.Append("<tr><td style='text-align:right'><B>" + TD + "</B> :</td><td style='text-align:left' >" + TodateString + "</td></tr>");
                    sb.Append("<table border='1'>");
                    sb.Append("<thead><tr>");


                    for (int i = 0; i < colCount; i++)
                    {
                        sb.Append("<td style='text-align: center;background-color:gray;'><B>");
                        sb.Append(Obj.dt.Columns[i].ColumnName.ToString());
                        sb.Append("</B></td>");
                    }
                    sb.Append("</tr></thead>");
                    for (int j = 0; j < Obj.dt.Rows.Count; j++)
                    {
                        sb.Append("<tr>");
                        for (int k = 0; k < colCount; k++)
                        {
                            string align = "left";
                            if (Obj.dt1.Rows[0].ItemArray[k].ToString() == "1") align = "center";
                            else if (Obj.dt1.Rows[0].ItemArray[k].ToString() == "2") align = "right";
                            sb.Append("<td 'style=text-align:" + align + ";'>");
                            if (Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ETOFromTime").ToString()) || Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ETOTotime").ToString()) || Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CustomerAccountNumber").ToString()))
                            {
                                string Value = "=concatenate(\"" + Obj.dt.Rows[j].ItemArray[k].ToString() + "\")";
                                sb.Append(Value);
                            }
                            else
                            {
                                sb.Append(Obj.dt.Rows[j].ItemArray[k].ToString());
                            }
                            sb.Append("</td>");
                        }
                        sb.Append("</tr>");
                    }
                    sb.Append("</table>");

                    if (Obj.exprtType == 1) // Excel Export
                    {
                        Obj.FileName = Obj.FileName + ".xls";

                        byte[] fileBytes = Encoding.UTF8.GetBytes(sb.ToString());


                        string base64String = Convert.ToBase64String(fileBytes);


                        return new JsonResult(new
                        {
                            FileName = Obj.FileName,
                            FileContent = base64String,
                            ContentType = "application/vnd.ms-excel"
                        });
                    }
                    else
                    {

                        Obj.FileName = Obj.FileName + ".pdf";
                        using (MemoryStream ms = new MemoryStream())
                        {
                            Document document = new Document(PageSize.A4);
                            PdfWriter writer = PdfWriter.GetInstance(document, ms);
                            document.Open();

                            // Header Title
                            Paragraph title = new Paragraph(Obj.Header, iTextSharp.text.FontFactory.GetFont("Arial", 16, iTextSharp.text.Font.BOLD))
                            {
                                Alignment = Element.ALIGN_CENTER
                            };
                            document.Add(title);

                            // Adding Company, Branch, and Date Details
                            document.Add(new Paragraph(cName + ": " + companyName, FontFactory.GetFont("Arial", 12)));
                            document.Add(new Paragraph(BName + ": " + BranchName, FontFactory.GetFont("Arial", 12)));
                            document.Add(new Paragraph($"{FD}: {FromdateString}", FontFactory.GetFont("Arial", 12)));
                            document.Add(new Paragraph($"{TD}: {TodateString}", FontFactory.GetFont("Arial", 12)));

                            // Adding space before the options table
                            document.Add(new Paragraph(" "));
                            document.Add(new Paragraph(" "));

                            // Options Table setup
                            PdfPTable optionsTable = new PdfPTable(2)
                            {
                                WidthPercentage = 100,    // Full page width
                                SpacingBefore = 5f       // Space before the table
                            };
                            document.Add(optionsTable);

                            // Adding space before the data table
                            document.Add(new Paragraph(" "));
                            document.Add(new Paragraph(" "));

                            // Data Table setup
                            PdfPTable dataTable = new PdfPTable(colCount)
                            {
                                WidthPercentage = 100,    // Full page width
                                SpacingBefore = 5f       // Space before the table
                            };

                            // Adding column headers with a gray background
                            foreach (DataColumn column in Obj.dt.Columns)
                            {
                                dataTable.AddCell(new PdfPCell(new Phrase(column.ColumnName))
                                {
                                    BackgroundColor = BaseColor.LIGHT_GRAY,
                                    HorizontalAlignment = Element.ALIGN_CENTER
                                });
                            }

                            // Adding data rows to the table
                            foreach (DataRow row in Obj.dt.Rows)
                            {
                                foreach (var cell in row.ItemArray)
                                {
                                    string value = cell.ToString();
                                    PdfPCell pdfCell = new PdfPCell(new Phrase(value))
                                    {
                                        HorizontalAlignment = Element.ALIGN_LEFT   // Default alignment
                                    };

                                    // If alignment logic is needed, implement here

                                    dataTable.AddCell(pdfCell);
                                }
                            }
                            document.Add(dataTable);

                            // Close the document
                            document.Close();

                            // Convert the memory stream to a byte array and return as JSON result
                            byte[] fileBytes = ms.ToArray();
                            string base64String = Convert.ToBase64String(fileBytes);

                            return new JsonResult(new
                            {
                                FileName = Obj.FileName,
                                FileContent = base64String,
                                ContentType = "application/pdf"
                            });
                        }
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return null;
            }
            #endregion
        }


        public static class ReportExportCR5
        {

            #region :::ReportExportCR5   Uday Kumar J B 21-08-2024 :::
            /// <summary>
            /// To Get ReportExportCR5 
            /// </summary>
            /// 
            public static async Task<JsonResult> Export(ExportReportExportCR5List Obj, string connString, int LogException)
            {

                string bid = (string)Obj.Branch;
                int BranchID = Convert.ToInt32(bid);
                string cName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CompanyName").ToString();
                string BName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "BranchName").ToString();
                string companyName = "";

                string BranchName = "";


                SqlCommand command = null;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Export_AM_ERP_Export_Common";



                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                            command.Parameters.AddWithValue("@BranchID", BranchID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    companyName = reader["Company_Name"] as string;
                                    BranchName = reader["Branch_Name"] as string;

                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                try
                {
                    int colCount = Obj.dt.Columns.Count;
                    if (Obj.exprtType == 1)
                    {
                        Obj.FileName = Obj.FileName + ".xls";
                        StringBuilder sb = new StringBuilder();
                        sb.Append("<table><tr><td colspan='" + colCount + "' style='text-align:center'>");
                        sb.Append("<B>" + Obj.Header + "</B>");
                        sb.Append("</td></tr>");
                        sb.Append("<tr></tr>");
                        sb.Append("<tr><td style='text-align:right'><B>" + cName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + companyName + "</td></tr>");
                        sb.Append("<tr><td style='text-align:right'><B>" + BName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + BranchName + "</td></tr><tr></tr></table>");
                        sb.Append("<table>");
                        for (int j = 0; j < Obj.Options.Columns.Count; j++)
                        {
                            sb.Append("<tr>");
                            for (int k = 0; k < (Obj.Options.Rows.Count); k++)
                            {
                                sb.Append("<td style='text-align:right'>");
                                sb.Append("<B>" + Obj.Options.Columns[j].ColumnName.ToString() + "</B> :</td><td style='text-align:left'> " + Obj.Options.Rows[k].ItemArray[j].ToString());
                                sb.Append("</td>");
                            }
                            sb.Append("</tr>");
                        }
                        sb.Append("<tr></tr>");
                        sb.Append("</table>");
                        sb.Append("<table>");
                        for (int k = 0; k < Obj.selection.Tables.Count; k++)
                        {
                            DataTable Local = Obj.selection.Tables[k];
                            sb.Append("<tr>");
                            sb.Append("<td style='text-align:right'><b>" + Local.Columns[0].ColumnName.ToString() + "</b> :</td>");
                            sb.Append("<td colspan='" + (colCount - 1) + "'>");
                            for (int i = 0; i < Local.Rows.Count; i++)
                            {
                                sb.Append(Local.Rows[i].ItemArray[0].ToString());
                            }
                            sb.Append("</td>");
                            sb.Append("</tr>");
                        }
                        sb.Append("<tr></tr>");
                        sb.Append("</table>");
                        sb.Append("<table border='1'>");
                        sb.Append("<thead><tr>");
                        for (int i = 0; i < colCount; i++)
                        {
                            sb.Append("<td style='text-align: center;background-color:gray;'>");
                            sb.Append("<b>" + Obj.dt.Columns[i].ColumnName.ToString() + "</b>");
                            sb.Append("</td>");
                        }
                        sb.Append("</tr></thead>");
                        for (int j = 0; j < Obj.dt.Rows.Count; j++)
                        {
                            sb.Append("<tr>");
                            for (int k = 0; k < colCount; k++)
                            {
                                string align = (Obj.Alignment.Rows[0].ItemArray[k].ToString() == "0" ? "left" : (Obj.Alignment.Rows[0].ItemArray[k].ToString() == "1" ? "center" : "right"));
                                sb.Append("<td  'style=text-align:'" + align + "';'>");
                                sb.Append(Obj.dt.Rows[j].ItemArray[k].ToString());
                                sb.Append("</td>");
                            }
                            sb.Append("</tr>");
                        }
                        sb.Append("</table>");
                        byte[] fileBytes = Encoding.UTF8.GetBytes(sb.ToString());


                        string base64String = Convert.ToBase64String(fileBytes);


                        return new JsonResult(new
                        {
                            FileName = Obj.FileName,
                            FileContent = base64String,
                            ContentType = "application/vnd.ms-excel"
                        });

                    }
                    else if (Obj.exprtType == 2)
                    {
                        Obj.FileName = Obj.FileName + ".pdf";
                        using (MemoryStream ms = new MemoryStream())
                        {
                            Document document = new Document(PageSize.A3);
                            PdfWriter writer = PdfWriter.GetInstance(document, ms);
                            document.Open();

                            // Header Title
                            Paragraph title = new Paragraph(Obj.Header, iTextSharp.text.FontFactory.GetFont("Arial", 16, iTextSharp.text.Font.BOLD))
                            {
                                Alignment = Element.ALIGN_CENTER
                            };
                            document.Add(title);

                            // Adding Company, Branch, and Date Details
                            document.Add(new Paragraph(cName + ": " + companyName, FontFactory.GetFont("Arial", 12)));
                            document.Add(new Paragraph(BName + ": " + BranchName, FontFactory.GetFont("Arial", 12)));

                            for (int j = 0; j < Obj.Options.Columns.Count; j++)
                            {
                                for (int k = 0; k < (Obj.Options.Rows.Count); k++)
                                {
                                    document.Add(new Paragraph(Obj.Options.Columns[j].ColumnName.ToString() + ": " + Obj.Options.Rows[k].ItemArray[j].ToString(), FontFactory.GetFont("Arial", 12)));
                                }
                            }
                            for (int k = 0; k < Obj.selection.Tables.Count; k++)
                            {
                                DataTable Local = Obj.selection.Tables[k];

                                // Get the column name from the first column
                                string columnName = Local.Columns[0].ColumnName.ToString();

                                // Create a string builder for appending values
                                StringBuilder paragraphText = new StringBuilder();
                                paragraphText.Append(columnName + ": ");

                                // Append all the values from the first column
                                for (int i = 0; i < Local.Rows.Count; i++)
                                {
                                    paragraphText.Append(Local.Rows[i][0].ToString() + " ");
                                }

                                // Add the constructed text as a new paragraph
                                document.Add(new Paragraph(paragraphText.ToString(), FontFactory.GetFont("Arial", 12)));
                            }
                            // Adding space before the options table
                            document.Add(new Paragraph(" "));
                            document.Add(new Paragraph(" "));

                            // Options Table setup
                            PdfPTable optionsTable = new PdfPTable(2)
                            {
                                WidthPercentage = 100,    // Full page width
                                SpacingBefore = 5f       // Space before the table
                            };
                            document.Add(optionsTable);

                            // Adding space before the data table
                            document.Add(new Paragraph(" "));
                            document.Add(new Paragraph(" "));

                            // Data Table setup
                            PdfPTable dataTable = new PdfPTable(colCount)
                            {
                                WidthPercentage = 100,    // Full page width
                                SpacingBefore = 5f       // Space before the table
                            };

                            // Adding column headers with a gray background
                            foreach (DataColumn column in Obj.dt.Columns)
                            {
                                dataTable.AddCell(new PdfPCell(new Phrase(column.ColumnName))
                                {
                                    BackgroundColor = BaseColor.LIGHT_GRAY,
                                    HorizontalAlignment = Element.ALIGN_CENTER
                                });
                            }

                            // Adding data rows to the table
                            foreach (DataRow row in Obj.dt.Rows)
                            {
                                foreach (var cell in row.ItemArray)
                                {
                                    string value = cell.ToString();
                                    PdfPCell pdfCell = new PdfPCell(new Phrase(value))
                                    {
                                        HorizontalAlignment = Element.ALIGN_LEFT   // Default alignment
                                    };

                                    // If alignment logic is needed, implement here

                                    dataTable.AddCell(pdfCell);
                                }
                            }
                            document.Add(dataTable);

                            // Close the document
                            document.Close();

                            // Convert the memory stream to a byte array and return as JSON result
                            byte[] fileBytes = ms.ToArray();
                            string base64String = Convert.ToBase64String(fileBytes);

                            return new JsonResult(new
                            {
                                FileName = Obj.FileName,
                                FileContent = base64String,
                                ContentType = "application/pdf"
                            });
                        }
                    }
                }
                catch (Exception ex) { }
                return null;
            }
            #endregion
        }


        public static class ReportExport1
        {


            #region :::ReportExport1   Uday Kumar J B 27-11-2024 :::
            /// <summary>
            /// To Get ReportExportCR5 
            /// </summary>
            /// 
            public static async Task<JsonResult> Export(ExportReportExport1List Obj, string connString, int LogException)
            {
                string cName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CompanyName").ToString();
                string BName = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "BranchName").ToString();
                string bid = Obj.Branch == null ? "0" : Obj.Branch.ToString();
                int BranchID = Convert.ToInt32(bid);
                string companyName = "";

                string BranchName = "";


                SqlCommand command = null;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Export_AM_ERP_Export_Common";



                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Company_ID", Obj.Company_ID);
                            command.Parameters.AddWithValue("@BranchID", BranchID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    companyName = reader["Company_Name"] as string;
                                    BranchName = reader["Branch_Name"] as string;

                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                //Logo added fot Excell export
                string ReportImagePath = "";
                if (System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath") != null)
                    ReportImagePath = System.Configuration.ConfigurationManager.AppSettings.Get("ReportImagePath").ToString();
                try
                {
                    int colCount = Obj.dt.Columns.Count;
                    if (Obj.exprtType == 1)
                    {
                        StringBuilder sb = new StringBuilder();
                        sb.Append("<table>");
                        if (ReportImagePath != "")
                            sb.Append("</tr><td <img src='" + ReportImagePath + "'></img></td><tr></tr>");
                        sb.Append("<tr></tr>");
                        sb.Append("<tr><td colspan='" + colCount + "' style='text-align:center'>");
                        sb.Append("<B>" + Obj.Header + "</B>");
                        sb.Append("</td></tr>");
                        sb.Append("<tr></tr>");
                        sb.Append("<tr><td style='text-align:right'><B>" + cName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + companyName + "</td></tr>");
                        sb.Append("<tr><td style='text-align:right'><B>" + BName + "</B> :</td><td colspan='" + (colCount - 1) + "' >" + BranchName + "</td></tr><tr></tr></table>");
                        sb.Append("<table>");
                        for (int j = 0; j < Obj.Options.Columns.Count; j++)
                        {
                            sb.Append("<tr>");
                            for (int k = 0; k < (Obj.Options.Rows.Count); k++)
                            {
                                sb.Append("<td style='text-align:right'>");
                                sb.Append("<B>" + Obj.Options.Columns[j].ColumnName.ToString() + "</B> :</td><td style='text-align:left'> " + Obj.Options.Rows[k].ItemArray[j].ToString());
                                sb.Append("</td>");
                            }
                            sb.Append("</tr>");
                        }
                        sb.Append("<tr></tr>");
                        sb.Append("</table>");
                        sb.Append("<table>");
                        for (int k = 0; k < Obj.selection.Tables.Count; k++)
                        {
                            DataTable Local = Obj.selection.Tables[k];
                            sb.Append("<tr>");
                            sb.Append("<td style='text-align:right'><b>" + Local.Columns[0].ColumnName.ToString() + "</b> :</td>");
                            sb.Append("<td colspan='" + (colCount - 1) + "'>");
                            for (int i = 0; i < Local.Rows.Count; i++)
                            {
                                sb.Append(Local.Rows[i].ItemArray[0].ToString());
                            }
                            sb.Append("</td>");
                            sb.Append("</tr>");
                        }
                        sb.Append("<tr></tr>");
                        sb.Append("</table>");
                        sb.Append("<table border='1'>");
                        sb.Append("<thead><tr>");
                        for (int i = 0; i < colCount; i++)
                        {
                            sb.Append("<td style='text-align: center;background-color:gray;'>");
                            sb.Append("<b>" + Obj.dt.Columns[i].ColumnName.ToString() + "</b>");
                            sb.Append("</td>");
                        }
                        sb.Append("</tr></thead>");
                        for (int j = 0; j < Obj.dt.Rows.Count; j++)
                        {
                            sb.Append("<tr>");
                            for (int k = 0; k < colCount; k++)
                            {
                                string align = (Obj.Alignment.Rows[0].ItemArray[k].ToString() == "0" ? "left" : (Obj.Alignment.Rows[0].ItemArray[k].ToString() == "1" ? "center" : "right"));
                                sb.Append("<td  'style=text-align:'" + align + "';'>");
                                if (Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "partnumberr").ToString()) || Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QtyIssuedReturned").ToString()) || Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "SAPInvoiceNumber").ToString()) || Obj.dt.Columns[k].ToString() == (CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CustomerAccountNumber").ToString()))
                                {
                                    string Value = "=concatenate(\"" + Obj.dt.Rows[j].ItemArray[k].ToString() + "\")";
                                    sb.Append(Value);
                                }
                                else
                                {
                                    sb.Append(Obj.dt.Rows[j].ItemArray[k].ToString());
                                }
                                sb.Append("</td>");
                            }
                            sb.Append("</tr>");
                        }
                        sb.Append("</table>");
                        byte[] fileBytes = Encoding.UTF8.GetBytes(sb.ToString());


                        string base64String = Convert.ToBase64String(fileBytes);


                        return new JsonResult(new
                        {
                            FileName = Obj.FileName,
                            FileContent = base64String,
                            ContentType = "application/vnd.ms-excel"
                        });
                    }
                    else if (Obj.exprtType == 2)
                    {
                        Obj.FileName = Obj.FileName + ".pdf";
                        using (MemoryStream ms = new MemoryStream())
                        {
                            Document document = new Document(PageSize.A3);
                            PdfWriter writer = PdfWriter.GetInstance(document, ms);
                            document.Open();

                            // Header Title
                            Paragraph title = new Paragraph(Obj.Header, iTextSharp.text.FontFactory.GetFont("Arial", 16, iTextSharp.text.Font.BOLD))
                            {
                                Alignment = Element.ALIGN_CENTER
                            };
                            document.Add(title);

                            // Adding Company, Branch, and Date Details
                            document.Add(new Paragraph(cName + ": " + companyName, FontFactory.GetFont("Arial", 12)));
                            document.Add(new Paragraph(BName + ": " + BranchName, FontFactory.GetFont("Arial", 12)));

                            for (int j = 0; j < Obj.Options.Columns.Count; j++)
                            {
                                for (int k = 0; k < (Obj.Options.Rows.Count); k++)
                                {
                                    document.Add(new Paragraph(Obj.Options.Columns[j].ColumnName.ToString() + ": " + Obj.Options.Rows[k].ItemArray[j].ToString(), FontFactory.GetFont("Arial", 12)));
                                }
                            }
                            for (int k = 0; k < Obj.selection.Tables.Count; k++)
                            {
                                DataTable Local = Obj.selection.Tables[k];

                                // Get the column name from the first column
                                string columnName = Local.Columns[0].ColumnName.ToString();

                                // Create a string builder for appending values
                                StringBuilder paragraphText = new StringBuilder();
                                paragraphText.Append(columnName + ": ");

                                // Append all the values from the first column
                                for (int i = 0; i < Local.Rows.Count; i++)
                                {
                                    paragraphText.Append(Local.Rows[i][0].ToString() + " ");
                                }

                                // Add the constructed text as a new paragraph
                                document.Add(new Paragraph(paragraphText.ToString(), FontFactory.GetFont("Arial", 12)));
                            }
                            // Adding space before the options table
                            document.Add(new Paragraph(" "));
                            document.Add(new Paragraph(" "));

                            // Options Table setup
                            PdfPTable optionsTable = new PdfPTable(2)
                            {
                                WidthPercentage = 100,    // Full page width
                                SpacingBefore = 5f       // Space before the table
                            };
                            document.Add(optionsTable);

                            // Adding space before the data table
                            document.Add(new Paragraph(" "));
                            document.Add(new Paragraph(" "));

                            // Data Table setup
                            PdfPTable dataTable = new PdfPTable(colCount)
                            {
                                WidthPercentage = 100,    // Full page width
                                SpacingBefore = 5f       // Space before the table
                            };

                            // Adding column headers with a gray background
                            foreach (DataColumn column in Obj.dt.Columns)
                            {
                                dataTable.AddCell(new PdfPCell(new Phrase(column.ColumnName))
                                {
                                    BackgroundColor = BaseColor.LIGHT_GRAY,
                                    HorizontalAlignment = Element.ALIGN_CENTER
                                });
                            }

                            // Adding data rows to the table
                            foreach (DataRow row in Obj.dt.Rows)
                            {
                                foreach (var cell in row.ItemArray)
                                {
                                    string value = cell.ToString();
                                    PdfPCell pdfCell = new PdfPCell(new Phrase(value))
                                    {
                                        HorizontalAlignment = Element.ALIGN_LEFT   // Default alignment
                                    };

                                    // If alignment logic is needed, implement here

                                    dataTable.AddCell(pdfCell);
                                }
                            }
                            document.Add(dataTable);

                            // Close the document
                            document.Close();

                            // Convert the memory stream to a byte array and return as JSON result
                            byte[] fileBytes = ms.ToArray();
                            string base64String = Convert.ToBase64String(fileBytes);

                            return new JsonResult(new
                            {
                                FileName = Obj.FileName,
                                FileContent = base64String,
                                ContentType = "application/pdf"
                            });
                        }
                    }
                }
                catch (Exception ex) { }

                return null;

            }
            #endregion

        }
    }
}