using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.DTOs;

namespace PBC.UtilityService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ExportController : ControllerBase
    {
        private readonly IExportService _exportService;

        public ExportController(IExportService exportService)
        {
            _exportService = exportService;
        }

        /// <summary>
        /// Exports data to a document.
        /// </summary>
        /// <returns></returns>
        [HttpPost("doc-export")]
        public async Task<IActionResult> DocExport([FromBody] ExportList ExportObj, [FromQuery] string connstring, [FromQuery] int LogException)
        {
            try
            {
                var result = await _exportService.DocExport(ExportObj, connstring, LogException);
                return result;
            }
            catch (Exception ex)
            {
                return StatusCode(500, "An error occurred while exporting data");
            }
        }
    }
}