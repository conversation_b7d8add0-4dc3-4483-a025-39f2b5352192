using PBC.UtilityService.Utilities.DTOs;
using PBC.UtilityService.Utilities;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PBC.UtilityService.Services
{
    public class ExportService : IExportService
    {
        public async Task<JsonResult> DocExport(ExportList ExportObj, string connstring, int LogException)
        {
            var result = default(dynamic);
            try
            {
                result = await DocumentExport.Export(ExportObj, connstring, LogException);
                return result;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
    }
}